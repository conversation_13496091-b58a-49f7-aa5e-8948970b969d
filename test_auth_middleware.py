"""
Test script to verify the authentication middleware is working correctly.
"""
import asyncio
import aiohttp
import json


async def test_api_without_auth():
    """Test that API endpoints are protected when no authentication is provided."""
    print("Testing API access without authentication...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test the therapist endpoint without authentication
            async with session.post(
                'http://localhost:8000/api/v1/therapist',
                json={'prompt': 'Hello, how are you?'},
                headers={'Content-Type': 'application/json'}
            ) as response:
                print(f"Status: {response.status}")
                response_data = await response.json()
                print(f"Response: {json.dumps(response_data, indent=2)}")
                
                if response.status == 401:
                    print("✅ SUCCESS: API endpoint is properly protected!")
                    return True
                else:
                    print("❌ FAILURE: API endpoint is not protected!")
                    return False
                    
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return False


async def test_static_files_access():
    """Test that static files are still accessible without authentication."""
    print("\nTesting static file access...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test accessing a static file (should work without auth)
            async with session.get('http://localhost:8000/static/js/chat.js') as response:
                print(f"Status: {response.status}")
                
                if response.status == 200:
                    print("✅ SUCCESS: Static files are accessible!")
                    return True
                else:
                    print("❌ FAILURE: Static files are not accessible!")
                    return False
                    
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return False


async def test_auth_pages_access():
    """Test that auth pages are accessible without authentication."""
    print("\nTesting auth pages access...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test accessing the login page (should work without auth)
            async with session.get('http://localhost:8000/login') as response:
                print(f"Status: {response.status}")
                
                if response.status == 200:
                    print("✅ SUCCESS: Login page is accessible!")
                    return True
                else:
                    print("❌ FAILURE: Login page is not accessible!")
                    return False
                    
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return False


async def main():
    """Run all tests."""
    print("🔒 Testing Authentication Middleware\n")
    print("Make sure the server is running on http://localhost:8000")
    print("=" * 50)
    
    results = []
    
    # Test API protection
    results.append(await test_api_without_auth())
    
    # Test static files still work
    results.append(await test_static_files_access())
    
    # Test auth pages still work
    results.append(await test_auth_pages_access())
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    if all(results):
        print("🎉 All tests passed! Authentication middleware is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
