# Authentication Middleware

This module provides authentication middleware and utilities for protecting API endpoints in the Therapist application.

## Overview

The authentication system uses session-based authentication with Google OAuth. Users authenticate through Google OAuth, and their session data is stored using FastAPI's SessionMiddleware.

## Components

### AuthenticationMiddleware

A FastAPI middleware that automatically protects specified API endpoints by validating user sessions.

**Features:**
- Protects API routes based on configurable path prefixes
- Validates session data for required fields
- Returns proper JSON error responses for unauthorized access
- Logs authentication attempts for security monitoring

**Usage:**
```python
from app.middleware.auth import AuthenticationMiddleware

# Add to FastAPI app
app.add_middleware(AuthenticationMiddleware, protected_paths=["/api/v1/"])
```

### Utility Functions

#### `get_current_user(request: Request) -> dict | None`

Safely retrieves the current authenticated user from the session.

**Returns:**
- `dict`: User data if authenticated and valid
- `None`: If not authenticated or invalid session

**Example:**
```python
from app.middleware import get_current_user

@router.get("/profile")
def get_profile(request: Request):
    user = get_current_user(request)
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    return {"email": user["email"], "name": user["name"]}
```

#### `require_auth(request: Request) -> dict`

Requires authentication and returns user data, raising an exception if not authenticated.

**Returns:**
- `dict`: User data if authenticated

**Raises:**
- `HTTPException`: 401 status if not authenticated

**Example:**
```python
from app.middleware import require_auth

@router.get("/secure-data")
def get_secure_data(request: Request):
    user = require_auth(request)  # Will raise 401 if not authenticated
    return {"data": "sensitive information", "user": user["email"]}
```

## Session Data Structure

The middleware expects user session data in the following format:

```python
{
    "sub": "google_user_id",      # Required: Google user ID
    "email": "<EMAIL>",  # Required: User email
    "name": "User Name",          # Optional: Display name
    "picture": "https://..."      # Optional: Profile picture URL
}
```

## Configuration

### Protected Paths

By default, the middleware protects all routes starting with `/api/v1/`. You can customize this:

```python
# Protect multiple path prefixes
app.add_middleware(
    AuthenticationMiddleware, 
    protected_paths=["/api/v1/", "/admin/", "/secure/"]
)

# Protect specific endpoints
app.add_middleware(
    AuthenticationMiddleware, 
    protected_paths=["/api/v1/therapist", "/api/v1/user-data"]
)
```

## Frontend Integration

The frontend should handle 401 responses by redirecting to the login page:

```javascript
const response = await fetch('/api/v1/therapist', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify(data)
});

if (response.status === 401) {
    window.location.href = '/login';
    return;
}
```

## Testing

Use the provided test script to verify the middleware is working:

```bash
# Start the server
uvicorn app.main:app --reload

# Run tests (in another terminal)
python test_auth_middleware.py
```

## Security Considerations

1. **Session Security**: Sessions are secured using the SECRET_KEY configuration
2. **HTTPS**: Use HTTPS in production to protect session cookies
3. **Session Expiration**: Consider implementing session timeout for enhanced security
4. **Logging**: Authentication failures are logged for security monitoring

## Error Responses

The middleware returns standardized error responses:

```json
{
    "detail": "Authentication required. Please log in to access this resource.",
    "error": "unauthorized"
}
```

This format is consistent with FastAPI's error handling and can be easily handled by frontend applications.
