from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi import Request, APIRouter

from app.pages import templates

router = APIRouter()

@router.get("/", response_class=HTMLResponse)
async def home(request: Request):
    # Require authentication
    if not request.session.get("user"):
        return RedirectResponse("/login", status_code=302)

    # Render the home page
    return templates.TemplateResponse("index.html", {"request": request, "user": request.session.get("user")})