import json
from markupsafe import Markup
from fastapi.templating import Jinja2Templates

templates = Jinja2Templates(directory="app/templates")

# Add a tojson filter for safe JSON embedding in templates
# Usage in templates: {{ value|tojson }}
# Ensures proper escaping and marks as safe for inclusion in <script> blocks

def _tojson(value):
    try:
        # Prevent </script> tag breakouts
        s = json.dumps(value).replace("</", "<\\/")
        return Markup(s)
    except Exception:
        # Fallback to string dump
        return Markup(json.dumps(str(value)))

templates.env.filters["tojson"] = _tojson