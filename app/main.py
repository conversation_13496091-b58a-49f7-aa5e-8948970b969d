from fastapi import <PERSON><PERSON><PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from starlette.middleware.sessions import SessionMiddleware

from app.core.config import settings
from app.pages.router import page_router
from app.api.v1.router import api_router
from app.auth.router import router as auth_router
from app.middleware.auth import AuthenticationMiddleware

app = FastAPI(title="Therapist Service", version="0.0.1")

# Authentication middleware to protect API endpoints (added first, executes last)
app.add_middleware(AuthenticationMiddleware, protected_paths=["/api/v1/"])

# Session middleware for storing auth session (added last, executes first)
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY, same_site="lax")

@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    return FileResponse("app/static/favicon.ico")

app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Auth routes
app.include_router(auth_router)

# App routes
app.include_router(page_router)
app.include_router(api_router, prefix="/api/v1")
