from app.store.db import DB

class MessageStore:
    async def save_message(self, user_id, message_id, parent_id, prompt, response: str):
        db = DB()
        try:
            await db.connect()
            await db.conn.execute('''
INSERT INTO public.messages
(user_id, provider_message_id, parent_message_id, prompt, response)
VALUES($1, $2, $3, $4, $5);''', user_id, message_id, parent_id, prompt, response)
            await db.disconnect()
        except Exception as e:
            print(f"Error saving message: {e}")
        finally:
            await db.disconnect()

    async def get_conversations_for_user(self, user_id: str):
        """
        Build conversations as disjoint threads (no intermingling) using a parent->children graph.
        - Each row is a user prompt + assistant response pair with a provider_message_id
        - parent_message_id points to the previous provider_message_id in the same thread
        """
        db = DB()
        try:
            await db.connect()

            rows = await db.conn.fetch('''
                SELECT
                    id,
                    provider_message_id,
                    parent_message_id,
                    prompt,
                    response,
                    created_date,
                    user_id
                FROM public.messages
                WHERE user_id = $1
                ORDER BY created_date ASC
            ''', user_id)

            if not rows:
                return []

            # Normalize messages and build lookups
            messages = []
            id_map: dict[str, dict] = {}
            for row in rows:
                m = {
                    'id': str(row['id']),
                    'provider_message_id': row['provider_message_id'],
                    'parent_message_id': row['parent_message_id'],
                    'prompt': row['prompt'],
                    'response': row['response'],
                    'created_date': row['created_date'],
                    'user_id': row['user_id'],
                }
                messages.append(m)
                id_map[m['provider_message_id']] = m

            # Build children adjacency list
            children: dict[str, list[str]] = {}
            for m in messages:
                p = m['parent_message_id']
                if p:
                    # Only link if parent exists among this user's messages
                    if p in id_map:
                        children.setdefault(p, []).append(m['provider_message_id'])
                    else:
                        # Treat as orphan child; will be handled as a root later
                        pass

            # Determine roots: no parent or parent missing from this user's set
            roots = [m for m in messages if not m['parent_message_id'] or m['parent_message_id'] not in id_map]
            # Sort roots by created_date ascending to build, later we'll sort conversations by started_at desc
            roots.sort(key=lambda x: x['created_date'])

            visited: set[str] = set()
            conversations = []

            def dfs_collect(root_id: str) -> list[dict]:
                stack = [root_id]
                collected: list[dict] = []
                while stack:
                    current_id = stack.pop()
                    if current_id in visited:
                        continue
                    visited.add(current_id)
                    node = id_map.get(current_id)
                    if not node:
                        continue
                    collected.append(node)
                    # Push children in reverse chronological order so that when popped, earlier ones are processed first
                    kids = children.get(current_id, [])
                    kids.sort(key=lambda cid: id_map[cid]['created_date'], reverse=True)
                    stack.extend(kids)
                # Ensure chronological order within the conversation
                collected.sort(key=lambda x: x['created_date'])
                return collected

            # Build conversations from roots
            for root in roots:
                rid = root['provider_message_id']
                if rid in visited:
                    continue
                conv_messages = dfs_collect(rid)
                if conv_messages:
                    started_at = min(msg['created_date'] for msg in conv_messages)
                    conversations.append({
                        'conversation_id': conv_messages[0]['provider_message_id'],
                        'started_at': started_at,
                        'messages': conv_messages,
                    })

            # Handle any remaining messages not connected to any root (safety)
            leftovers = [m for m in messages if m['provider_message_id'] not in visited]
            for m in leftovers:
                conv_messages = dfs_collect(m['provider_message_id'])
                if conv_messages:
                    started_at = min(msg['created_date'] for msg in conv_messages)
                    conversations.append({
                        'conversation_id': conv_messages[0]['provider_message_id'],
                        'started_at': started_at,
                        'messages': conv_messages,
                    })

            # Sort conversations by most recent start time first
            conversations.sort(key=lambda c: c['started_at'], reverse=True)
            return conversations

        except Exception as e:
            print(f"Error fetching conversations: {e}")
            return []
        finally:
            await db.disconnect()



