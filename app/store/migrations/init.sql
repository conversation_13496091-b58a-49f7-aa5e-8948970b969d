CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE public.messages (
	id uuid DEFAULT uuid_generate_v4() NOT NULL,
	provider_message_id varchar NOT NULL,
	parent_message_id varchar NULL,
	prompt text NOT NULL,
	response text NOT NULL,
	created_date timestamp DEFAULT now() NOT NULL,
	user_id varchar NOT NULL,
	CONSTRAINT messages_pk PRIMARY KEY (id),
	CONSTRAINT messages_unique UNIQUE (provider_message_id),
	CONSTRAINT messages_messages_fk FOREIGN KEY (parent_message_id) REFERENCES public.messages(provider_message_id)
);